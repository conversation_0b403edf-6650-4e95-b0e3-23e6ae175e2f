require('dotenv').config();
const APIClient = require('./lib/api-client');
const WebSocketClient = require('./lib/websocket-client');
const TestDataGenerator = require('./lib/test-data');
const TestLogger = require('./lib/test-logger');

class DualUserTest {
  constructor() {
    this.logger = TestLogger.create('dual-user-test');
    this.dataGenerator = new TestDataGenerator();
    this.users = [];
    this.parallelUsers = parseInt(process.env.PARALLEL_USERS) || 2;
  }

  async run() {
    try {
      this.logger.info(`Starting Dual User E2E Test with ${this.parallelUsers} users`);
      
      // Initialize users
      await this.initializeUsers();
      
      // Run parallel user flows
      await this.runParallelUserFlows();
      
      this.logger.success('Dual User E2E Test completed successfully');
      
    } catch (error) {
      this.logger.error('Dual User E2E Test failed', error);
      throw error;
    } finally {
      await this.disconnectAll();
      await this.logger.saveReport();
    }
  }

  async initializeUsers() {
    this.logger.step('Initialize Users', 1);
    
    for (let i = 0; i < this.parallelUsers; i++) {
      const userData = this.dataGenerator.generateTestSuite(1)[0];
      
      const user = {
        id: i + 1,
        apiClient: new APIClient(),
        wsClient: new WebSocketClient(),
        testData: userData,
        jobId: null,
        resultId: null,
        conversationId: null,
        status: 'initialized'
      };
      
      this.users.push(user);
      
      this.logger.success(`User ${user.id} initialized`, {
        email: userData.user.email,
        username: userData.user.username
      });
    }
  }

  async runParallelUserFlows() {
    this.logger.step('Run Parallel User Flows', 2);
    
    const userPromises = this.users.map(user => this.runSingleUserFlow(user));
    
    try {
      const results = await Promise.allSettled(userPromises);
      
      let successCount = 0;
      let failureCount = 0;
      
      results.forEach((result, index) => {
        const user = this.users[index];
        
        if (result.status === 'fulfilled') {
          successCount++;
          this.logger.success(`User ${user.id} flow completed successfully`);
        } else {
          failureCount++;
          this.logger.error(`User ${user.id} flow failed`, result.reason);
        }
      });
      
      this.logger.info(`Parallel execution completed: ${successCount} success, ${failureCount} failures`);
      
      if (failureCount > 0) {
        throw new Error(`${failureCount} user flows failed`);
      }
      
    } catch (error) {
      this.logger.error('Parallel user flows failed', error);
      throw error;
    }
  }

  async runSingleUserFlow(user) {
    try {
      this.logger.userAction(user.id, 'Starting user flow');
      
      // Register
      await this.registerUser(user);
      
      // Login
      await this.loginUser(user);
      
      // Connect WebSocket
      await this.connectWebSocket(user);
      
      // Update Profile
      await this.updateProfile(user);
      
      // Submit Assessment
      await this.submitAssessment(user);
      
      // Wait for Notification
      await this.waitForNotification(user);
      
      // Get Profile Persona
      await this.getProfilePersona(user);
      
      // Test Chatbot
      await this.testChatbot(user);
      
      // Cleanup
      await this.cleanup(user);
      
      user.status = 'completed';
      this.logger.userAction(user.id, 'User flow completed successfully');
      
    } catch (error) {
      user.status = 'failed';
      this.logger.userAction(user.id, `User flow failed: ${error.message}`);
      throw error;
    }
  }

  async registerUser(user) {
    this.logger.userAction(user.id, 'Registering user');
    
    try {
      const response = await user.apiClient.register(user.testData.user);
      
      if (response.success && response.data.user && response.data.token) {
        this.logger.userAction(user.id, 'User registered successfully', {
          userId: response.data.user.id,
          email: response.data.user.email
        });
      } else {
        throw new Error('Registration response missing required data');
      }
    } catch (error) {
      this.logger.userAction(user.id, 'User registration failed');
      throw error;
    }
  }

  async loginUser(user) {
    this.logger.userAction(user.id, 'Logging in user');
    
    try {
      const credentials = {
        email: user.testData.user.email,
        password: user.testData.user.password
      };
      
      const response = await user.apiClient.login(credentials);
      
      if (response.success && response.data.token) {
        this.logger.userAction(user.id, 'User logged in successfully');
      } else {
        throw new Error('Login response missing token');
      }
    } catch (error) {
      this.logger.userAction(user.id, 'User login failed');
      throw error;
    }
  }

  async connectWebSocket(user) {
    this.logger.userAction(user.id, 'Connecting WebSocket');
    
    try {
      await user.wsClient.connect();
      await user.wsClient.authenticate(user.apiClient.token);
      
      this.logger.userAction(user.id, 'WebSocket connected and authenticated');
    } catch (error) {
      this.logger.userAction(user.id, 'WebSocket connection failed');
      throw error;
    }
  }

  async updateProfile(user) {
    this.logger.userAction(user.id, 'Updating profile');
    
    try {
      const response = await user.apiClient.updateProfile(user.testData.profileUpdate);
      
      if (response.success) {
        this.logger.userAction(user.id, 'Profile updated successfully', {
          username: user.testData.profileUpdate.username
        });
      } else {
        throw new Error('Profile update failed');
      }
    } catch (error) {
      this.logger.userAction(user.id, 'Profile update failed');
      throw error;
    }
  }

  async submitAssessment(user) {
    this.logger.userAction(user.id, 'Submitting assessment');
    
    try {
      const response = await user.apiClient.submitAssessment(user.testData.assessment);
      
      if (response.success && response.data.jobId) {
        user.jobId = response.data.jobId;
        this.logger.userAction(user.id, 'Assessment submitted successfully', {
          jobId: user.jobId,
          status: response.data.status
        });
      } else {
        throw new Error('Assessment submission failed');
      }
    } catch (error) {
      this.logger.userAction(user.id, 'Assessment submission failed');
      throw error;
    }
  }

  async waitForNotification(user) {
    this.logger.userAction(user.id, 'Waiting for WebSocket notification');
    
    try {
      const notification = await user.wsClient.waitForAssessmentComplete(
        user.jobId, 
        parseInt(process.env.ASSESSMENT_TIMEOUT) || 300000
      );
      
      user.resultId = notification.resultId;
      this.logger.userAction(user.id, 'Assessment completion notification received', {
        jobId: notification.jobId,
        resultId: user.resultId
      });
      
    } catch (error) {
      this.logger.userAction(user.id, 'Failed to receive notification');
      throw error;
    }
  }

  async getProfilePersona(user) {
    this.logger.userAction(user.id, 'Getting profile persona');
    
    try {
      if (!user.resultId) {
        throw new Error('No result ID available');
      }
      
      const response = await user.apiClient.getResult(user.resultId);
      
      if (response.success && response.data.persona_profile) {
        this.logger.userAction(user.id, 'Profile persona retrieved successfully', {
          archetype: response.data.persona_profile.archetype,
          careerCount: response.data.persona_profile.careerRecommendation?.length || 0
        });
      } else {
        throw new Error('Failed to retrieve persona profile');
      }
    } catch (error) {
      this.logger.userAction(user.id, 'Failed to get profile persona');
      throw error;
    }
  }

  async testChatbot(user) {
    this.logger.userAction(user.id, 'Testing chatbot');
    
    try {
      // Create conversation from assessment
      const convResponse = await user.apiClient.createConversationFromAssessment({
        assessment_id: user.resultId,
        title: user.testData.conversation.title,
        auto_start_message: true
      });
      
      if (convResponse.success && convResponse.data.conversation) {
        user.conversationId = convResponse.data.conversation.id;
        this.logger.userAction(user.id, 'Chatbot conversation created', {
          conversationId: user.conversationId
        });
      } else {
        throw new Error('Failed to create chatbot conversation');
      }
      
      // Send test messages (limited for parallel testing)
      const messagesToSend = user.testData.messages.slice(0, 2); // Only first 2 messages
      
      for (let i = 0; i < messagesToSend.length; i++) {
        const message = messagesToSend[i];
        
        const msgResponse = await user.apiClient.sendMessage(user.conversationId, message);
        
        if (msgResponse.success) {
          this.logger.userAction(user.id, `Message ${i + 1} sent and responded`);
        } else {
          throw new Error(`Failed to send message ${i + 1}`);
        }
        
        // Small delay between messages
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      this.logger.userAction(user.id, 'Chatbot interaction completed successfully');
      
    } catch (error) {
      this.logger.userAction(user.id, 'Chatbot test failed');
      throw error;
    }
  }

  async cleanup(user) {
    if (process.env.ENABLE_CLEANUP !== 'true') {
      this.logger.userAction(user.id, 'Skipping cleanup (ENABLE_CLEANUP is not true)');
      return;
    }
    
    this.logger.userAction(user.id, 'Cleaning up test account');
    
    try {
      await user.apiClient.deleteAccount();
      this.logger.userAction(user.id, 'Test account cleaned up successfully');
    } catch (error) {
      this.logger.userAction(user.id, 'Account cleanup failed (non-critical)');
    }
  }

  async disconnectAll() {
    try {
      for (const user of this.users) {
        if (user.wsClient) {
          user.wsClient.disconnect();
        }
      }
      this.logger.info('Disconnected all users from services');
    } catch (error) {
      this.logger.warning('Disconnect error (non-critical)', error);
    }
  }
}

// Run test if called directly
if (require.main === module) {
  const test = new DualUserTest();
  test.run()
    .then(() => {
      console.log('\n✅ Dual User Test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Dual User Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = DualUserTest;
