const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

class SimpleArchiveTest {
  constructor() {
    this.baseURL = 'http://localhost:3000/api';
    this.token = null;
    this.userId = null;
    this.resultId = null;
  }

  async registerUser() {
    const userData = {
      email: `testuser${Date.now()}@example.com`,
      username: `testuser${Math.random().toString(36).substring(7)}`,
      password: 'TestPassword123!',
      full_name: 'Test User'
    };

    console.log('🔄 Registering user...');
    const response = await axios.post(`${this.baseURL}/auth/register`, userData);
    
    if (response.status === 201) {
      this.userId = response.data.data.user.id;
      console.log('✅ User registered successfully:', this.userId);
      return userData;
    }
    throw new Error('Registration failed');
  }

  async loginUser(userData) {
    console.log('🔄 Logging in user...');
    const response = await axios.post(`${this.baseURL}/auth/login`, {
      email: userData.email,
      password: userData.password
    });

    if (response.status === 200) {
      this.token = response.data.data.token;
      console.log('✅ User logged in successfully');
      return this.token;
    }
    throw new Error('Login failed');
  }

  async submitAssessment() {
    console.log('🔄 Submitting assessment...');
    const assessmentData = {
      assessment_name: 'Test Assessment',
      responses: {
        riasec: {
          realistic: [4, 5, 3, 4, 2],
          investigative: [3, 4, 5, 3, 4],
          artistic: [2, 3, 4, 5, 3],
          social: [5, 4, 3, 2, 4],
          enterprising: [3, 2, 4, 3, 5],
          conventional: [2, 3, 3, 4, 3]
        }
      }
    };

    const response = await axios.post(`${this.baseURL}/assessment/submit`, assessmentData, {
      headers: { Authorization: `Bearer ${this.token}` }
    });

    if (response.status === 200) {
      const jobId = response.data.data.jobId;
      console.log('✅ Assessment submitted successfully:', jobId);
      return jobId;
    }
    throw new Error('Assessment submission failed');
  }

  async waitForAssessmentCompletion(jobId) {
    console.log('🔄 Waiting for assessment completion...');
    let attempts = 0;
    const maxAttempts = 30; // 30 seconds

    while (attempts < maxAttempts) {
      try {
        const response = await axios.get(`${this.baseURL}/assessment/status/${jobId}`, {
          headers: { Authorization: `Bearer ${this.token}` }
        });

        const status = response.data.data.status;
        console.log(`⏳ Assessment status: ${status}`);

        if (status === 'completed') {
          this.resultId = response.data.data.result_id;
          console.log('✅ Assessment completed, result ID:', this.resultId);
          return this.resultId;
        } else if (status === 'failed') {
          throw new Error('Assessment processing failed');
        }

        await new Promise(resolve => setTimeout(resolve, 1000));
        attempts++;
      } catch (error) {
        console.log(`⚠️ Error checking status: ${error.message}`);
        attempts++;
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    throw new Error('Assessment completion timeout');
  }

  async testArchiveEndpoint() {
    console.log('🔄 Testing archive/results/:id endpoint...');
    
    if (!this.resultId) {
      throw new Error('No result ID available for testing');
    }

    try {
      const response = await axios.get(`${this.baseURL}/archive/results/${this.resultId}`, {
        headers: { Authorization: `Bearer ${this.token}` }
      });

      if (response.status === 200 && response.data.success) {
        console.log('✅ Archive endpoint test PASSED');
        console.log('📊 Result data received:', {
          id: response.data.data.id,
          status: response.data.data.status,
          hasPersonaProfile: !!response.data.data.persona_profile
        });
        return true;
      } else {
        console.log('❌ Archive endpoint test FAILED - Invalid response');
        return false;
      }
    } catch (error) {
      console.log('❌ Archive endpoint test FAILED:', error.response?.data || error.message);
      return false;
    }
  }

  async run() {
    try {
      console.log('🚀 Starting Simple Archive Test\n');

      // Step 1: Register and login
      const userData = await this.registerUser();
      await this.loginUser(userData);

      // Step 2: Submit assessment
      const jobId = await this.submitAssessment();

      // Step 3: Wait for completion
      await this.waitForAssessmentCompletion(jobId);

      // Step 4: Test archive endpoint
      const success = await this.testArchiveEndpoint();

      console.log('\n📊 Test Summary');
      console.log('================');
      if (success) {
        console.log('✅ Archive endpoint fix: SUCCESS');
        console.log('🎉 The X-User-Email header fix is working correctly!');
      } else {
        console.log('❌ Archive endpoint fix: FAILED');
        console.log('🔧 The fix may need additional investigation');
      }

      return success;

    } catch (error) {
      console.log('\n❌ Test failed:', error.message);
      return false;
    }
  }
}

// Run the test
if (require.main === module) {
  const test = new SimpleArchiveTest();
  test.run().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Test error:', error);
    process.exit(1);
  });
}

module.exports = SimpleArchiveTest;
