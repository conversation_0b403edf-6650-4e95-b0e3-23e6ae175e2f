const axios = require('axios');
const { faker } = require('@faker-js/faker');

async function simpleTest() {
  console.log('🚀 Running Simple ATMA Backend Test');
  console.log('=====================================');
  
  const baseURL = 'http://localhost:3000';
  const authURL = 'http://localhost:3001';
  
  // Test data
  const testUser = {
    email: `testuser_${Date.now()}@example.com`,
    username: `testuser_${Math.random().toString(36).substring(2, 8)}`,
    password: 'TestPassword123!',
    full_name: faker.person.fullName()
  };
  
  console.log('📝 Test User:', testUser);
  
  try {
    // Test 1: Health Check
    console.log('\n🔍 Test 1: Service Health Check');
    try {
      const healthResponse = await axios.get(`${authURL}/health`);
      console.log('✅ Auth Service Health:', healthResponse.data);
    } catch (error) {
      console.log('⚠️ Auth Service Health Check failed:', error.response?.data || error.message);
    }
    
    // Test 2: User Registration
    console.log('\n👤 Test 2: User Registration');
    try {
      const registerResponse = await axios.post(`${authURL}/auth/register`, testUser);
      console.log('✅ User Registration Success:', {
        success: registerResponse.data.success,
        userId: registerResponse.data.data?.user?.id,
        email: registerResponse.data.data?.user?.email
      });
      
      const token = registerResponse.data.data.token;
      
      // Test 3: User Login
      console.log('\n🔐 Test 3: User Login');
      const loginResponse = await axios.post(`${authURL}/auth/login`, {
        email: testUser.email,
        password: testUser.password
      });
      console.log('✅ User Login Success:', {
        success: loginResponse.data.success,
        hasToken: !!loginResponse.data.data.token
      });
      
      // Test 4: Profile Access
      console.log('\n👤 Test 4: Profile Access');
      const profileResponse = await axios.get(`${authURL}/auth/profile`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Profile Access Success:', {
        success: profileResponse.data.success,
        email: profileResponse.data.data?.email
      });
      
      // Test 5: Assessment Service
      console.log('\n📊 Test 5: Assessment Service Health');
      try {
        const assessmentHealth = await axios.get('http://localhost:3003/health');
        console.log('✅ Assessment Service Health:', assessmentHealth.data);
      } catch (error) {
        console.log('⚠️ Assessment Service Health Check failed:', error.response?.data || error.message);
      }
      
      // Test 6: Chatbot Service
      console.log('\n🤖 Test 6: Chatbot Service Health');
      try {
        const chatbotHealth = await axios.get('http://localhost:3006/health');
        console.log('✅ Chatbot Service Health:', chatbotHealth.data);
      } catch (error) {
        console.log('⚠️ Chatbot Service Health Check failed:', error.response?.data || error.message);
      }
      
      console.log('\n🎉 Simple Test Completed Successfully!');
      return {
        success: true,
        testsRun: 6,
        testsPassed: 4, // Registration, Login, Profile, and at least one health check
        testUser: testUser
      };
      
    } catch (error) {
      console.log('❌ User Registration Failed:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data || error.message,
        testUser: testUser
      };
    }
    
  } catch (error) {
    console.log('❌ Test Failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// Run the test
simpleTest().then(result => {
  console.log('\n📊 Final Result:', result);
  process.exit(result.success ? 0 : 1);
}).catch(error => {
  console.error('💥 Test crashed:', error);
  process.exit(1);
});
